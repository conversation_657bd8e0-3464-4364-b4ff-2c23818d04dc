// 路由别名
export enum RoutesAlias {
  Home = '/index/index', // 首页
  Login = '/login', // 登录
  Register = '/register', // 注册
  ForgetPassword = '/forget-password', // 忘记密码
  Exception403 = '/exception/403', // 403
  Exception404 = '/exception/404', // 404
  Exception500 = '/exception/500', // 500
  Success = '/result/Success', // 成功
  Fail = '/result/Fail', // 失败
  Dashboard = '/dashboard/console', // 工作台
  Analysis = '/dashboard/analysis',
  Fireworks = '/dashboard/fireworks',
  Chat = '/dashboard/chat',
  PlanLog = '/dashboard/plan-log',

  Server = '/safeguard/Server', // 服务器

  // 快递相关路由
  ExpressOrderList = '/express/OrderList', // 订单列表
  // （已移除重设计预览路由）

  // 余额管理路由
  BalanceManagement = '/balance/BalanceManagement', // 余额管理

  // 地址解析路由
  AddressParse = '/address/AddressParse', // 地址解析

  // 回调管理路由
  CallbackList = '/callback/CallbackList', // 回调列表

  // 工单管理路由
  WorkOrderList = '/workorder/WorkOrderList', // 工单列表
  WorkOrderCreate = '/workorder/WorkOrderCreate', // 创建工单
  WorkOrderDetail = '/workorder/WorkOrderDetail', // 工单详情

  // 定价页面
  Pricing = '/template/pricing' // 定价页面
}
