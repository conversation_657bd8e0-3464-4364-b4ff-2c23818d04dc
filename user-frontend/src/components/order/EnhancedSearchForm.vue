<template>
  <MagicCard class="p-6 mb-6">
    <div class="flex items-center justify-between mb-4">
      <h3 class="text-lg font-semibold text-gray-900 dark:text-white flex items-center gap-2">
        <el-icon class="text-blue-500">
          <Search />
        </el-icon>
        搜索筛选
      </h3>
      
      <div class="flex items-center gap-2">
        <ShimmerButton
          :shimmer-color="'#10b981'"
          :background="'rgba(16, 185, 129, 0.1)'"
          class="text-green-600"
          @click="handleSearch"
        >
          <el-icon class="mr-1">
            <Search />
          </el-icon>
          搜索
        </ShimmerButton>
        
        <ShimmerButton
          :shimmer-color="'#6b7280'"
          :background="'rgba(107, 114, 128, 0.1)'"
          class="text-gray-600"
          @click="handleReset"
        >
          <el-icon class="mr-1">
            <Refresh />
          </el-icon>
          重置
        </ShimmerButton>
      </div>
    </div>

    <el-form :model="searchForm" inline class="enhanced-search-form">
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
        <!-- 平台订单号 -->
        <el-form-item label="平台订单号">
          <el-input
            v-model="searchForm.order_no"
            placeholder="请输入平台订单号"
            clearable
            class="w-full"
          >
            <template #prefix>
              <el-icon class="text-gray-400">
                <Document />
              </el-icon>
            </template>
          </el-input>
        </el-form-item>

        <!-- 客户订单号 -->
        <el-form-item label="客户订单号">
          <el-input
            v-model="searchForm.customer_order_no"
            placeholder="请输入客户订单号"
            clearable
            class="w-full"
          >
            <template #prefix>
              <el-icon class="text-gray-400">
                <User />
              </el-icon>
            </template>
          </el-input>
        </el-form-item>

        <!-- 运单号 -->
        <el-form-item label="运单号">
          <el-input
            v-model="searchForm.tracking_no"
            placeholder="点击进行批量查询"
            clearable
            class="w-full cursor-pointer"
            readonly
            @click="$emit('open-batch-search')"
          >
            <template #prefix>
              <el-icon class="text-gray-400">
                <Box />
              </el-icon>
            </template>
            <template #suffix>
              <el-icon class="text-blue-500 cursor-pointer">
                <Search />
              </el-icon>
            </template>
          </el-input>
        </el-form-item>

        <!-- 订单状态 -->
        <el-form-item label="订单状态">
          <el-select
            v-model="searchForm.status"
            placeholder="请选择状态"
            clearable
            class="w-full"
          >
            <el-option
              v-for="status in statusOptions"
              :key="status.value"
              :label="status.label"
              :value="status.value"
            >
              <div class="flex items-center gap-2">
                <div 
                  :class="getStatusDotClass(status.value)"
                  class="w-2 h-2 rounded-full"
                />
                {{ status.label }}
              </div>
            </el-option>
          </el-select>
        </el-form-item>

        <!-- 快递公司 -->
        <el-form-item label="快递公司">
          <el-select
            v-model="searchForm.express_type"
            placeholder="请选择快递公司"
            clearable
            class="w-full"
          >
            <el-option
              v-for="company in expressCompanies"
              :key="company.code"
              :label="company.name"
              :value="company.code"
            />
          </el-select>
        </el-form-item>

        <!-- 供应商 -->
        <el-form-item label="供应商">
          <el-select
            v-model="searchForm.provider"
            placeholder="请选择供应商"
            clearable
            class="w-full"
          >
            <el-option
              v-for="provider in providerOptions"
              :key="provider.value"
              :label="provider.label"
              :value="provider.value"
            >
              <el-tag :type="getProviderTagType(provider.value)" size="small">
                {{ provider.label }}
              </el-tag>
            </el-option>
          </el-select>
        </el-form-item>

        <!-- 创建时间 -->
        <el-form-item label="创建时间">
          <el-date-picker
            v-model="dateRange"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            format="YYYY-MM-DD HH:mm"
            value-format="YYYY-MM-DD HH:mm:ss"
            class="w-full"
            @change="handleDateRangeChange"
          />
        </el-form-item>

        <!-- 快速时间选择 -->
        <el-form-item label="快速选择">
          <el-select
            v-model="currentQuickTime"
            placeholder="选择时间范围"
            clearable
            class="w-full"
            @change="handleQuickTimeChange"
          >
            <el-option
              v-for="option in quickTimeOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </el-form-item>
      </div>
    </el-form>
  </MagicCard>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import MagicCard from '@/components/ui/magic-card.vue'
import ShimmerButton from '@/components/ui/shimmer-button.vue'
import { Search, Refresh, Document, User, Box } from '@element-plus/icons-vue'

interface SearchForm {
  order_no?: string
  customer_order_no?: string
  tracking_no?: string
  status?: string
  express_type?: string
  provider?: string
  start_time?: string
  end_time?: string
}

interface Props {
  searchForm: SearchForm
  statusOptions: Array<{ value: string; label: string }>
  expressCompanies: Array<{ code: string; name: string }>
  providerOptions: Array<{ value: string; label: string }>
  quickTimeOptions: Array<{ value: string; label: string }>
  dateRange: [string, string] | null
  currentQuickTime: string
}

const props = defineProps<Props>()

const emit = defineEmits<{
  'search': []
  'reset': []
  'open-batch-search': []
  'date-range-change': [value: [string, string] | null]
  'quick-time-change': [value: string]
}>()

const handleSearch = () => {
  emit('search')
}

const handleReset = () => {
  emit('reset')
}

const handleDateRangeChange = (value: [string, string] | null) => {
  emit('date-range-change', value)
}

const handleQuickTimeChange = (value: string) => {
  emit('quick-time-change', value)
}

// 状态点样式
const getStatusDotClass = (status: string) => {
  const dotClasses = {
    'pending': 'bg-yellow-500',
    'processing': 'bg-blue-500',
    'shipped': 'bg-purple-500',
    'delivered': 'bg-green-500',
    'cancelled': 'bg-red-500',
    'failed': 'bg-red-500'
  }
  return dotClasses[status as keyof typeof dotClasses] || 'bg-gray-500'
}

// 供应商标签类型
const getProviderTagType = (provider: string) => {
  const typeMap: Record<string, any> = {
    'jd': 'primary',
    'cainiao': 'success',
    'dbl': 'warning'
  }
  return typeMap[provider] || 'info'
}
</script>

<style scoped>
.enhanced-search-form :deep(.el-form-item) {
  margin-bottom: 16px;
}

.enhanced-search-form :deep(.el-form-item__label) {
  font-weight: 500;
  color: #374151;
}

.dark .enhanced-search-form :deep(.el-form-item__label) {
  color: #d1d5db;
}
</style>
