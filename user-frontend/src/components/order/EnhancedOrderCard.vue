<template>
  <MagicCard 
    :class="cn(
      'group relative overflow-hidden transition-all duration-300 hover:scale-[1.02]',
      getStatusCardClass(order.status),
      className
    )"
    :show-spotlight="true"
    :show-border-glow="true"
  >
    <!-- 状态指示器 -->
    <div class="absolute top-4 right-4">
      <div 
        :class="cn(
          'flex items-center gap-2 rounded-full px-3 py-1 text-xs font-medium',
          getStatusBadgeClass(order.status)
        )"
      >
        <div 
          :class="cn(
            'h-2 w-2 rounded-full',
            getStatusDotClass(order.status)
          )"
        />
        {{ order.status_desc }}
      </div>
    </div>

    <!-- 订单头部信息 -->
    <div class="mb-4">
      <div class="flex items-start justify-between">
        <div class="flex-1">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
            {{ order.order_no }}
          </h3>
          <p v-if="order.customer_order_no" class="text-sm text-gray-500 dark:text-gray-400">
            客户订单号: {{ order.customer_order_no }}
          </p>
        </div>
      </div>
      
      <!-- 运单号和快递公司 -->
      <div class="mt-3 flex items-center gap-4">
        <div class="flex items-center gap-2">
          <el-icon class="text-blue-500">
            <Box />
          </el-icon>
          <span class="text-sm font-medium">{{ order.tracking_no || '暂无运单号' }}</span>
        </div>
        <div class="flex items-center gap-2">
          <el-tag 
            :type="getProviderTagType(order.provider)" 
            size="small"
            effect="light"
          >
            {{ getExpressName(order) }}
          </el-tag>
        </div>
      </div>
    </div>

    <!-- 地址信息 -->
    <div class="mb-4 space-y-2">
      <div class="flex items-start gap-3">
        <el-icon class="mt-1 text-green-500">
          <LocationFilled />
        </el-icon>
        <div class="flex-1">
          <div class="text-sm font-medium text-gray-700 dark:text-gray-300">寄件人</div>
          <div class="text-sm text-gray-600 dark:text-gray-400">{{ order.sender_info }}</div>
        </div>
      </div>
      <div class="flex items-start gap-3">
        <el-icon class="mt-1 text-red-500">
          <LocationFilled />
        </el-icon>
        <div class="flex-1">
          <div class="text-sm font-medium text-gray-700 dark:text-gray-300">收件人</div>
          <div class="text-sm text-gray-600 dark:text-gray-400">{{ order.receiver_info }}</div>
        </div>
      </div>
    </div>

    <!-- 订单详情 -->
    <div class="mb-4 grid grid-cols-2 gap-4">
      <div class="space-y-1">
        <div class="text-xs text-gray-500 dark:text-gray-400">重量</div>
        <div class="text-sm font-medium">{{ order.weight }}kg</div>
      </div>
      <div class="space-y-1">
        <div class="text-xs text-gray-500 dark:text-gray-400">价格</div>
        <div class="text-sm font-medium text-orange-600">¥{{ order.price?.toFixed(2) }}</div>
      </div>
      <div class="space-y-1">
        <div class="text-xs text-gray-500 dark:text-gray-400">实际费用</div>
        <div class="text-sm font-medium text-green-600">¥{{ order.actual_fee?.toFixed(2) }}</div>
      </div>
      <div class="space-y-1">
        <div class="text-xs text-gray-500 dark:text-gray-400">创建时间</div>
        <div class="text-sm">{{ formatDateTime(order.created_at) }}</div>
      </div>
    </div>

    <!-- 操作按钮 -->
    <div class="flex gap-2">
      <ShimmerButton
        :shimmer-color="'#3b82f6'"
        :background="'rgba(59, 130, 246, 0.1)'"
        class="flex-1 text-blue-600 hover:text-blue-700"
        @click="$emit('view-detail', order)"
      >
        <el-icon class="mr-1">
          <View />
        </el-icon>
        查看详情
      </ShimmerButton>
      
      <ShimmerButton
        v-if="order.can_cancel"
        :shimmer-color="'#ef4444'"
        :background="'rgba(239, 68, 68, 0.1)'"
        class="text-red-600 hover:text-red-700"
        @click="$emit('cancel-order', order)"
      >
        <el-icon>
          <Close />
        </el-icon>
      </ShimmerButton>
      
      <ShimmerButton
        v-if="order.can_retry"
        :shimmer-color="'#10b981'"
        :background="'rgba(16, 185, 129, 0.1)'"
        class="text-green-600 hover:text-green-700"
        @click="$emit('retry-order', order)"
      >
        <el-icon>
          <Refresh />
        </el-icon>
      </ShimmerButton>
    </div>

    <!-- 悬浮动画效果 -->
    <div class="absolute inset-0 opacity-0 transition-opacity duration-300 group-hover:opacity-100">
      <div class="absolute inset-0 bg-gradient-to-r from-blue-500/5 via-purple-500/5 to-pink-500/5" />
    </div>
  </MagicCard>
</template>

<script setup lang="ts">
import { cn } from '@/lib/utils'
import MagicCard from '@/components/ui/magic-card.vue'
import ShimmerButton from '@/components/ui/shimmer-button.vue'
import { Box, LocationFilled, View, Close, Refresh } from '@element-plus/icons-vue'
import type { OrderListItem } from '@/api/model/kuaidiModel'

interface Props {
  order: OrderListItem
  className?: string
}

const props = defineProps<Props>()

const emit = defineEmits<{
  'view-detail': [order: OrderListItem]
  'cancel-order': [order: OrderListItem]
  'retry-order': [order: OrderListItem]
}>()

// 状态相关样式
const getStatusCardClass = (status: string) => {
  const statusClasses = {
    'pending': 'border-yellow-200 bg-yellow-50 dark:border-yellow-800 dark:bg-yellow-900/20',
    'processing': 'border-blue-200 bg-blue-50 dark:border-blue-800 dark:bg-blue-900/20',
    'shipped': 'border-purple-200 bg-purple-50 dark:border-purple-800 dark:bg-purple-900/20',
    'delivered': 'border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-900/20',
    'cancelled': 'border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-900/20',
    'failed': 'border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-900/20'
  }
  return statusClasses[status as keyof typeof statusClasses] || 'border-gray-200 bg-gray-50'
}

const getStatusBadgeClass = (status: string) => {
  const badgeClasses = {
    'pending': 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400',
    'processing': 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400',
    'shipped': 'bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-400',
    'delivered': 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400',
    'cancelled': 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400',
    'failed': 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400'
  }
  return badgeClasses[status as keyof typeof badgeClasses] || 'bg-gray-100 text-gray-800'
}

const getStatusDotClass = (status: string) => {
  const dotClasses = {
    'pending': 'bg-yellow-500 animate-pulse',
    'processing': 'bg-blue-500 animate-pulse',
    'shipped': 'bg-purple-500 animate-pulse',
    'delivered': 'bg-green-500',
    'cancelled': 'bg-red-500',
    'failed': 'bg-red-500'
  }
  return dotClasses[status as keyof typeof dotClasses] || 'bg-gray-500'
}

// 工具函数
const getExpressName = (order: OrderListItem) => {
  return order.express_name || '未知快递'
}

const getProviderTagType = (provider: string) => {
  const typeMap: Record<string, any> = {
    'jd': 'primary',
    'cainiao': 'success',
    'dbl': 'warning'
  }
  return typeMap[provider] || 'info'
}

const formatDateTime = (dateStr: string) => {
  if (!dateStr) return '-'
  return new Date(dateStr).toLocaleString('zh-CN')
}
</script>
