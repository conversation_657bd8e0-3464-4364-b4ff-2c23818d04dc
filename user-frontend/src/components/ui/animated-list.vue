<template>
  <div
    :class="cn('relative flex flex-col gap-2 overflow-hidden', className)"
    :style="{ height: `${height}px` }"
  >
    <TransitionGroup
      name="list"
      tag="div"
      class="flex flex-col gap-2"
    >
      <div
        v-for="(item, index) in visibleItems"
        :key="item.id || index"
        :class="cn(
          'flex items-center gap-3 rounded-lg border bg-white p-3 shadow-sm transition-all duration-300 hover:shadow-md dark:bg-gray-900',
          itemClassName
        )"
        :style="{ animationDelay: `${index * delay}ms` }"
      >
        <slot :item="item" :index="index">
          <!-- 默认渲染 -->
          <div class="flex-1">
            <div class="font-medium">{{ item.title || item.name }}</div>
            <div v-if="item.description" class="text-sm text-gray-500">
              {{ item.description }}
            </div>
          </div>
          <div v-if="item.time" class="text-xs text-gray-400">
            {{ item.time }}
          </div>
        </slot>
      </div>
    </TransitionGroup>
    
    <!-- 渐变遮罩 -->
    <div 
      v-if="showGradient"
      class="pointer-events-none absolute inset-x-0 top-0 h-8 bg-gradient-to-b from-background to-transparent"
    />
    <div 
      v-if="showGradient"
      class="pointer-events-none absolute inset-x-0 bottom-0 h-8 bg-gradient-to-t from-background to-transparent"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { cn } from '@/lib/utils'

interface ListItem {
  id?: string | number
  title?: string
  name?: string
  description?: string
  time?: string
  [key: string]: any
}

interface Props {
  items: ListItem[]
  className?: string
  itemClassName?: string
  height?: number
  delay?: number
  showGradient?: boolean
  autoScroll?: boolean
  scrollInterval?: number
}

const props = withDefaults(defineProps<Props>(), {
  height: 300,
  delay: 100,
  showGradient: true,
  autoScroll: false,
  scrollInterval: 3000
})

const currentIndex = ref(0)
const intervalId = ref<NodeJS.Timeout>()

const visibleItems = computed(() => {
  if (!props.autoScroll) return props.items
  
  const maxVisible = Math.floor(props.height / 60) // 假设每项高度约60px
  const start = currentIndex.value
  const end = start + maxVisible
  
  return props.items.slice(start, end)
})

const startAutoScroll = () => {
  if (!props.autoScroll || props.items.length === 0) return
  
  intervalId.value = setInterval(() => {
    const maxVisible = Math.floor(props.height / 60)
    const maxStart = Math.max(0, props.items.length - maxVisible)
    
    currentIndex.value = (currentIndex.value + 1) % (maxStart + 1)
  }, props.scrollInterval)
}

const stopAutoScroll = () => {
  if (intervalId.value) {
    clearInterval(intervalId.value)
    intervalId.value = undefined
  }
}

onMounted(() => {
  startAutoScroll()
})

onUnmounted(() => {
  stopAutoScroll()
})
</script>

<style scoped>
.list-enter-active,
.list-leave-active {
  transition: all 0.3s ease;
}

.list-enter-from {
  opacity: 0;
  transform: translateY(-20px);
}

.list-leave-to {
  opacity: 0;
  transform: translateY(20px);
}

.list-move {
  transition: transform 0.3s ease;
}
</style>
