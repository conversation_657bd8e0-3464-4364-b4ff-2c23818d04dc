<template>
  <div
    ref="cardRef"
    :class="cn(
      'relative h-full w-full overflow-hidden rounded-xl border border-gray-200 bg-white p-6 shadow-sm transition-all duration-300 hover:shadow-lg dark:border-gray-800 dark:bg-gray-900',
      className
    )"
    @mouseenter="handleMouseEnter"
    @mouseleave="handleMouseLeave"
    @mousemove="handleMouseMove"
  >
    <!-- 光效背景 -->
    <div
      v-if="showSpotlight"
      class="pointer-events-none absolute inset-0 opacity-0 transition-opacity duration-300"
      :class="{ 'opacity-100': isHovered }"
      :style="{
        background: `radial-gradient(circle at ${mousePosition.x}px ${mousePosition.y}px, rgba(59, 130, 246, 0.1) 0%, transparent 50%)`
      }"
    />
    
    <!-- 边框光效 -->
    <div
      v-if="showBorderGlow"
      class="absolute inset-0 rounded-xl opacity-0 transition-opacity duration-300"
      :class="{ 'opacity-100': isHovered }"
      :style="{
        background: `linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.3), transparent)`,
        maskImage: 'linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0)',
        maskComposite: 'xor',
        WebkitMaskComposite: 'xor',
        padding: '1px'
      }"
    />
    
    <!-- 内容区域 -->
    <div class="relative z-10">
      <slot />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { cn } from '@/lib/utils'

interface Props {
  className?: string
  showSpotlight?: boolean
  showBorderGlow?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  showSpotlight: true,
  showBorderGlow: true
})

const cardRef = ref<HTMLElement>()
const isHovered = ref(false)
const mousePosition = reactive({ x: 0, y: 0 })

const handleMouseEnter = () => {
  isHovered.value = true
}

const handleMouseLeave = () => {
  isHovered.value = false
}

const handleMouseMove = (event: MouseEvent) => {
  if (!cardRef.value) return
  
  const rect = cardRef.value.getBoundingClientRect()
  mousePosition.x = event.clientX - rect.left
  mousePosition.y = event.clientY - rect.top
}
</script>
