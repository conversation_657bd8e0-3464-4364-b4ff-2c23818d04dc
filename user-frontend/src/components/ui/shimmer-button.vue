<template>
  <button
    :class="cn(
      'group relative z-0 flex cursor-pointer items-center justify-center overflow-hidden whitespace-nowrap rounded-lg border border-white/10 px-4 py-2 text-white transition-transform duration-300 ease-in-out active:translate-y-px',
      'transform-gpu',
      className
    )"
    :style="{
      '--spread': '90deg',
      '--shimmer-color': shimmerColor,
      '--radius': borderRadius,
      '--speed': shimmerDuration,
      '--cut': shimmerSize,
      '--bg': background,
      background: 'var(--bg)',
      borderRadius: 'var(--radius)'
    }"
    v-bind="$attrs"
    @click="$emit('click', $event)"
  >
    <!-- spark container -->
    <div class="-z-30 absolute inset-0 overflow-visible blur-[2px]">
      <!-- spark -->
      <div class="absolute inset-0 h-[100cqh] animate-shimmer-slide aspect-square border-radius-0">
        <!-- spark before -->
        <div 
          class="absolute -inset-full w-auto rotate-0 animate-spin-around translate-0"
          :style="{
            background: `conic-gradient(from calc(270deg-(var(--spread)*0.5)), transparent 0, var(--shimmer-color) var(--spread), transparent var(--spread))`
          }"
        />
      </div>
    </div>
    
    <slot />

    <!-- Highlight -->
    <div class="absolute inset-0 size-full rounded-2xl px-4 py-1.5 text-sm font-medium shadow-[inset_0_-8px_10px_#ffffff1f] transform-gpu transition-all duration-300 ease-in-out group-hover:shadow-[inset_0_-6px_10px_#ffffff3f] group-active:shadow-[inset_0_-10px_10px_#ffffff3f]" />

    <!-- backdrop -->
    <div 
      class="absolute -z-20"
      :style="{
        background: 'var(--bg)',
        borderRadius: 'var(--radius)',
        inset: 'var(--cut)'
      }"
    />
  </button>
</template>

<script setup lang="ts">
import { cn } from '@/lib/utils'

interface Props {
  shimmerColor?: string
  shimmerSize?: string
  borderRadius?: string
  shimmerDuration?: string
  background?: string
  className?: string
}

const props = withDefaults(defineProps<Props>(), {
  shimmerColor: '#ffffff',
  shimmerSize: '0.05em',
  shimmerDuration: '3s',
  borderRadius: '100px',
  background: 'rgba(0, 0, 0, 1)'
})

defineEmits<{
  click: [event: MouseEvent]
}>()
</script>

<style scoped>
@keyframes shimmer-slide {
  to {
    transform: translate(calc(100cqw - 100%), -100cqh);
  }
}

@keyframes spin-around {
  to {
    transform: rotate(360deg);
  }
}

.animate-shimmer-slide {
  animation: shimmer-slide var(--speed) infinite linear;
}

.animate-spin-around {
  animation: spin-around var(--speed) infinite linear;
}
</style>
